@echo off
title Drillmex Desktop Application
echo ========================================
echo    Drillmex Desktop Application
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo Node.js version: 
node --version
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    echo This may take a few minutes on first run...
    npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
    echo Dependencies installed successfully!
    echo.
)

REM Check if all required files exist
if not exist "app.js" (
    echo ERROR: app.js not found
    pause
    exit /b 1
)

if not exist "public\index.html" (
    echo ERROR: public\index.html not found
    pause
    exit /b 1
)

REM Start the application
echo Launching Drillmex Desktop Application...
echo.
echo If the application doesn't start, please check the console for errors.
echo You can close this window after the application starts.
echo.

npm start

REM If we get here, the application has closed
echo.
echo Application closed.
pause