{"name": "drillmex-desktop", "version": "1.0.0", "description": "Drillmex Desktop Application - Price Matching Tool", "main": "app.js", "homepage": "./", "scripts": {"start": "electron app.js", "dev": "NODE_ENV=development electron app.js", "build": "electron-builder", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir", "build-exe": "electron-builder --win portable", "build-single": "electron-builder --win portable --x64"}, "build": {"appId": "com.drillmex.desktop", "productName": "Drillmex Desktop", "directories": {"output": "dist"}, "files": ["app.js", "src/**/*", "public/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}, "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "xlsx": "^0.18.5", "electron": "^27.0.0"}, "devDependencies": {"electron-builder": "^24.6.4"}, "author": "Drillmex Team", "license": "ISC", "keywords": ["electron", "desktop", "price-matching", "excel", "data-processing"]}