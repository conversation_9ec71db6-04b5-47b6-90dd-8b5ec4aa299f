const path = require('path');
const { writeTXT, generateTimestamp } = require('../utils/fileUtils');

/**
 * Service class for generating reports and output files
 */
class ReportService {
  
  /**
   * Generates all output files and reports
   * @param {Object} matchingResults - Results from price matching
   * @param {Object} priceResults - Results from price processing
   * @param {Object} fileInfo - Information about uploaded files
   * @returns {Object} File paths and statistics
   */
  static generateReports(matchingResults, priceResults, fileInfo) {
    const timestamp = generateTimestamp();
    
    const {
      completeExportData,
      unmatchedItems,
      matchCount,
      emptyVendprodCount,
      itemsNotInExport,
      topUnmatchedItems
    } = matchingResults;
    
    const {
      validPriceItems,
      invalidPriceItems,
      priceData
    } = priceResults;
    
    const { exportFile, priceFile, exportData } = fileInfo;
    
    // Generate file paths
    const filePaths = this.generateFilePaths(timestamp);
    
    // Write main output files - now using complete export data instead of just matched items
    writeTXT(filePaths.matchedPath, completeExportData);
    writeTXT(filePaths.unmatchedPath, unmatchedItems);
    
    // Write additional report files
    if (invalidPriceItems.length > 0) {
      this.writeInvalidPricesReport(filePaths.invalidPricesPath, invalidPriceItems);
    }
    
    if (itemsNotInExport.length > 0) {
      writeTXT(filePaths.notInExportPath, itemsNotInExport);
    }
    
        
    // Log file creation
    this.logFileCreation(filePaths, invalidPriceItems, itemsNotInExport);

    return {
      filePaths,
      stats: this.calculateStats(exportData, matchCount, validPriceItems, invalidPriceItems,
                                 itemsNotInExport, emptyVendprodCount)
    };
  }
  
  /**
   * Generates file paths for all output files
   * @param {string} timestamp - Timestamp for file naming
   * @returns {Object} Object containing all file paths
   */
  static generateFilePaths(timestamp) {
    const publicDir = path.join(process.cwd(), 'public');
    return {
      matchedPath: path.join(publicDir, `UNITED_ABRASIVES_ICSW_complete_export_updated_${timestamp}.txt`),
      unmatchedPath: path.join(publicDir, `unmatched_only_${timestamp}.txt`),
      invalidPricesPath: path.join(publicDir, `invalid_prices_in_excel_${timestamp}.txt`),
      notInExportPath: path.join(publicDir, `not_in_export_${timestamp}.txt`)
    };
  }
  
  /**
   * Writes invalid prices report
   * @param {string} filePath - Path to write the file
   * @param {Array} invalidPriceItems - Array of invalid price items
   */
  static writeInvalidPricesReport(filePath, invalidPriceItems) {
    const invalidPricesData = invalidPriceItems.map(item => ({
      itemNumber: item.itemNumber || 'MISSING',
      price: item.price || 'MISSING',
      reason: item.reason
    }));
    writeTXT(filePath, invalidPricesData);
  }
  
  /**
   * Logs file creation to console
   * @param {Object} filePaths - Object containing file paths
   * @param {Array} invalidPriceItems - Invalid price items
   * @param {Array} itemsNotInExport - Items not in export
   */
  static logFileCreation(filePaths, invalidPriceItems, itemsNotInExport) {
    console.log(`Written complete export file with updated prices to ${filePaths.matchedPath}`);
    console.log(`Written unmatched items to ${filePaths.unmatchedPath}`);
    
    if (invalidPriceItems.length > 0) {
      console.log(`Written invalid price items to ${filePaths.invalidPricesPath}`);
    }
    if (itemsNotInExport.length > 0) {
      console.log(`Written items not in export to ${filePaths.notInExportPath}`);
    }
  }
  
  /**
   * Calculates statistics for the response
   * @param {Array} exportData - Export data
   * @param {number} matchCount - Number of matched items
   * @param {number} validPriceItems - Number of valid price items
   * @param {Array} invalidPriceItems - Invalid price items
   * @param {Array} itemsNotInExport - Items not in export
   * @param {number} emptyVendprodCount - Count of empty vendor product codes
   * @returns {Object} Statistics object
   */
  static calculateStats(exportData, matchCount, validPriceItems, invalidPriceItems,
                       itemsNotInExport, emptyVendprodCount) {
    return {
      totalItems: exportData.length,
      matchedItems: matchCount,
      unmatchedItems: exportData.length - matchCount,
      priceItems: validPriceItems,
      invalidPriceItems: invalidPriceItems.length,
      itemsNotInExport: itemsNotInExport.length,
      emptyVendprodCount: emptyVendprodCount
    };
  }
  
  /**
   * Gets file names without paths for response
   * @param {Object} filePaths - Object containing file paths
   * @param {Array} invalidPriceItems - Invalid price items
   * @param {Array} itemsNotInExport - Items not in export
   * @returns {Object} File names for response
   */
  static getResponseFileNames(filePaths, invalidPriceItems, itemsNotInExport) {
    return {
      matchedFile: path.basename(filePaths.matchedPath),
      unmatchedFile: path.basename(filePaths.unmatchedPath),
      invalidPricesFile: invalidPriceItems.length > 0 ? path.basename(filePaths.invalidPricesPath) : null,
      notInExportFile: itemsNotInExport.length > 0 ? path.basename(filePaths.notInExportPath) : null
    };
  }
}

module.exports = ReportService;