@echo off
title Drillmex File Cleanup Utility
echo ========================================
echo    Drillmex File Cleanup Utility
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo This utility helps you clean up generated files.
echo.
echo Options:
echo 1. View file statistics
echo 2. Delete files older than 7 days
echo 3. Delete all generated files
echo 4. Exit
echo.

:menu
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto stats
if "%choice%"=="2" goto cleanup_old
if "%choice%"=="3" goto cleanup_all
if "%choice%"=="4" goto exit
echo Invalid choice. Please enter 1, 2, 3, or 4.
goto menu

:stats
echo.
echo Checking file statistics...
node -e "
const CleanupService = require('./src/services/cleanupService');
const stats = CleanupService.getCleanupStats();
const files = CleanupService.getGeneratedFiles();

console.log('=== FILE STATISTICS ===');
console.log('Total Files:', stats.totalFiles);
console.log('Total Size:', CleanupService.formatFileSize(stats.totalSize));
console.log('Files from Today:', stats.filesByAge.today);
console.log('Files from This Week:', stats.filesByAge.thisWeek);
console.log('Files from This Month:', stats.filesByAge.thisMonth);
console.log('Older Files:', stats.filesByAge.older);
console.log('');

if (files.length > 0) {
  console.log('=== RECENT FILES ===');
  files.slice(0, 10).forEach((file, i) => {
    console.log((i+1) + '.', file.name, '-', CleanupService.formatFileSize(file.size));
  });
} else {
  console.log('No generated files found.');
}
"
echo.
goto menu

:cleanup_old
echo.
echo This will delete all generated files older than 7 days.
set /p confirm="Are you sure? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo.
echo Deleting old files...
node -e "
const CleanupService = require('./src/services/cleanupService');
const result = CleanupService.cleanupOldFiles(7);
console.log(result.message);
if (result.deletedFiles.length > 0) {
  console.log('Deleted files:');
  result.deletedFiles.forEach(file => console.log('  -', file));
}
if (result.errors.length > 0) {
  console.log('Errors:');
  result.errors.forEach(error => console.log('  -', error.file, ':', error.error));
}
"
echo.
goto menu

:cleanup_all
echo.
echo WARNING: This will delete ALL generated files!
echo This action cannot be undone.
set /p confirm="Are you sure? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo.
echo Deleting all generated files...
node -e "
const CleanupService = require('./src/services/cleanupService');
const result = CleanupService.cleanupAllFiles();
console.log(result.message);
if (result.deletedFiles.length > 0) {
  console.log('Deleted files:');
  result.deletedFiles.forEach(file => console.log('  -', file));
}
if (result.errors.length > 0) {
  console.log('Errors:');
  result.errors.forEach(error => console.log('  -', error.file, ':', error.error));
}
"
echo.
goto menu

:exit
echo.
echo Cleanup utility closed.
pause
exit /b 0
