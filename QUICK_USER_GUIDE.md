# Drillmex User Guide

## 1. Index

1. Disclaimer  
2. Index  
3. Problem Statement  
4. Summary  
5. Requirements  
6. Solution Adviser  
7. Technical Details  
8. Start Guide  

---

## 2. Disclaimer

Drillmex is provided "as is" without warranty of any kind. The authors and distributors are not liable for any damages or data loss resulting from the use or misuse of this application. Always back up your data before processing files with Drillmex.

## 3. Problem Statement

Manually matching and updating product prices between export files from business systems (like SAAMM) and supplier price lists is time-consuming, error-prone, and inefficient. Discrepancies in product codes, missing data, and formatting issues often lead to incorrect pricing, lost revenue, and increased administrative workload. Businesses need a reliable, automated solution to streamline this process and ensure pricing accuracy.

---

## 4. Summary

Drillmex is a desktop application designed to automate and simplify the process of matching and updating product prices between your SAAMM export files and Excel price lists. With a user-friendly interface, it enables quick uploads, efficient processing, and clear results, helping businesses maintain accurate pricing with minimal manual effort.

---

## 5. Requirements

- **Operating System:** Windows 10 or later
- **Memory:** At least 4GB RAM
- **Disk Space:** 100MB free
- **Node.js:** Version 14 or higher (auto-installed if missing)
- **Files Needed:**
  - SAAMM Export File (.txt, tab-separated, must include `vendprod` column)
  - Price Excel File (.xlsx, must include `Item #` and `BASE PRICE` columns)

---

## 6. Solution Adviser

- Ensure your export and price files follow the required formats (see above).
- Always check the statistics and error messages after processing to address unmatched or invalid items.
- Use the output files to update your system and review any discrepancies.
- For best results, keep your product codes consistent across all files.

---

## 7. Technical Details

- **Language:** HTML,CSS, Javascript, Node.js, Express.js
- **Startup:** `start.bat` automates dependency installation and app launch
- **Output:** Generates updated, unmatched, invalid, and extra item reports with timestamps

---

## 8. Start Guide

1. **Download** the Drillmex folder to your computer.
2. **Double-click** `start.bat` to launch the application.
3. **Upload** your SAAMM export file (.txt) and price file (.xlsx) using the interface.
4. **Click** "Upload & Process Files" to begin processing.
5. **Review** the results panel for download links and statistics.
6. **Download** the output files and use them as needed for your business processes. 