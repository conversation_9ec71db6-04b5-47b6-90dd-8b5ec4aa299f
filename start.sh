#!/bin/bash

echo "========================================"
echo "    Drillmex Desktop Application"
echo "========================================"
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "Node.js version: $(node --version)"
echo

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    echo "This may take a few minutes on first run..."
    npm install
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to install dependencies"
        echo "Please check your internet connection and try again"
        exit 1
    fi
    echo "Dependencies installed successfully!"
    echo
fi

# Check if required files exist
if [ ! -f "app.js" ]; then
    echo "ERROR: app.js not found"
    exit 1
fi

if [ ! -f "public/index.html" ]; then
    echo "ERROR: public/index.html not found"
    exit 1
fi

# Start the application
echo "Launching Drillmex Desktop Application..."
echo
echo "If the application doesn't start, please check the console for errors."
echo

npm start

echo
echo "Application closed."