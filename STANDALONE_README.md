# Drillmex Desktop - Standalone Application

## Quick Start

### Windows
1. Double-click `start.bat` to launch the application
2. The application will automatically install dependencies on first run
3. Wait for the desktop window to open

### macOS/Linux
1. Open terminal in this directory
2. Run: `chmod +x start.sh` (first time only)
3. Run: `./start.sh`
4. Wait for the desktop window to open

## Requirements

- **Node.js 14 or higher** - Download from [nodejs.org](https://nodejs.org/)
- **Windows 10+, macOS 10.14+, or Linux** (Ubuntu 18.04+)
- **4GB RAM minimum**
- **100MB free disk space**

## Features

- ✅ **Standalone Desktop Application** - No browser required
- ✅ **Automatic Port Detection** - Finds available port automatically
- ✅ **File Processing** - Handle large Excel and TSV files
- ✅ **Price Matching** - Intelligent exact and fuzzy matching
- ✅ **Multiple Output Formats** - Complete export, unmatched items, analysis files
- ✅ **Real-time Statistics** - Live progress and matching statistics
- ✅ **Error Handling** - Comprehensive error reporting and recovery

## File Formats Supported

### Input Files
- **Export File**: Tab-separated values (.txt) with 'vendprod' column
- **Price File**: Excel spreadsheet (.xlsx) with 'Item #' and 'BASE PRICE' columns

### Output Files
- **Complete Export**: All items with updated prices where matches found
- **Unmatched Items**: Items that couldn't be price-matched
- **Invalid Prices**: Price file entries with data issues
- **Items Not in Export**: Price items not found in export file

## Troubleshooting

### Application Won't Start
1. **Check Node.js**: Ensure Node.js is installed and in PATH
2. **Check Dependencies**: Delete `node_modules` folder and restart
3. **Check Ports**: Ensure no other applications are using ports 3000-3010
4. **Check Permissions**: Ensure write permissions in application directory

### File Processing Errors
1. **File Format**: Ensure files match required formats exactly
2. **File Size**: Maximum 50MB per file
3. **File Encoding**: Use UTF-8 encoding for text files
4. **Column Names**: Ensure exact column names ('vendprod', 'Item #', 'BASE PRICE')

### Performance Issues
1. **Large Files**: Processing time increases with file size
2. **Memory**: Ensure sufficient RAM for large datasets
3. **Disk Space**: Ensure enough space for output files

## Building Standalone Executables

To create distributable executables:

```bash
# Install electron-builder (if not already installed)
npm install electron-builder --save-dev

# Build for current platform
npm run build

# Build for all platforms
npm run dist

# Create portable version (Windows)
npm run pack
```

Built applications will be in the `dist/` directory.

## Security Features

- ✅ **Isolated Environment** - Runs in secure Electron container
- ✅ **Local Processing** - No data sent to external servers
- ✅ **File Validation** - Input validation and sanitization
- ✅ **Automatic Cleanup** - Temporary files automatically removed

## Support

For issues or questions:
1. Check this README and documentation files
2. Review console output for error messages
3. Ensure all requirements are met
4. Contact Drillmex support team

## Version Information

- **Application Version**: 1.0.0
- **Electron Version**: 27.0.0
- **Node.js Required**: 14.0.0+