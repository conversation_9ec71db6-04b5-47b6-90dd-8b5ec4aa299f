{"appId": "com.drillmex.desktop", "productName": "Drillmex Desktop", "directories": {"output": "dist", "buildResources": "assets"}, "files": ["app.js", "src/**/*", "public/**/*", "node_modules/**/*", "package.json", "!node_modules/.cache", "!**/*.map", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "publisherName": "Drillmex Team"}, "mac": {"target": "dmg", "category": "public.app-category.productivity"}, "linux": {"target": ["AppImage", "deb"], "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Drillmex Desktop"}, "portable": {"artifactName": "DrillmexDesktop-Portable.exe"}}