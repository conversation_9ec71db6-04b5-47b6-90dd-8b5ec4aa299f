# Drillmex User Guide

## Table of Contents
1. [Getting Started](#getting-started)
2. [Application Overview](#application-overview)
3. [File Requirements](#file-requirements)
4. [Step-by-Step Usage](#step-by-step-usage)
5. [Understanding Results](#understanding-results)

## Getting Started

### System Requirements
- Windows 10 or later
- Node.js 14 or higher (automatically handled)
- At least 4GB RAM
- 100MB free disk space

### Installation
1. Download the Drillmex application folder
2. Double-click `start.bat` to launch the application
3. The application will automatically:
   - Install required dependencies (first run only)
   - Start the desktop application
   - Open in a new window

**Screenshot Location: Main application window opening**

## Application Overview

When you launch Drillmex, you'll see a clean, professional interface with two main sections:

### Left Panel: Input Files
- **Export File Upload**: For your SAAMM export file (.txt)
- **Price File Upload**: For your Excel price file (.xlsx)
- **Upload & Process Button**: Starts the price matching process

### Right Panel: Results
- **Download Links**: For processed files
- **Statistics**: Detailed matching statistics
- **Error Messages**: If any issues occur

**Screenshot Location: Main interface showing both panels**

## File Requirements

### Export File (SAAMM Export File)
- **Format**: Tab-separated values (TSV)
- **Required Column**: `vendprod` (product codes)
- **Example Structure**:
  ```
  vendprod    description    baseprice    category
  ABC123      Product A      10.50    Tools
  XYZ789      Product B      25.00    Hardware
  ```

### Price File (Excel Price File)
- **Format**: Excel spreadsheet
- **Required Columns**: 
  - `Item #` 
  - `BASE PRICE` 
- **Example Structure**:
  ```
  Item #    Description    BASE PRICE    Category
  ABC123    Product A      12.00         Tools
  XYZ789    Product B      28.50         Hardware
  ```

**Screenshot Location: File format examples**

## Step-by-Step Usage

### Step 1: Launch the Application
1. Navigate to your Drillmex folder
2. Double-click `start.bat`
3. Wait for the application window to open

**Screenshot Location: start.bat file and loading screen**

### Step 2: Upload Export File
1. Click the **"Choose File"** button under "Export File from SAAMM (.txt)"
2. Navigate to your export file
3. Select the .txt file containing your product data
4. You'll see the filename appear next to the button

**Screenshot Location: File selection dialog and selected file**

### Step 3: Upload Price File
1. Click the **"Choose File"** button under "Price Excel File (.xlsx)"
2. Navigate to your price file
3. Select the .xlsx file containing your pricing data
4. You'll see the filename appear next to the button

**Screenshot Location: Excel file selection and both files selected**

### Step 4: Process Files
1. Click the **"Upload & Process Files"** button
2. A loading overlay will appear with a spinner
3. The message "Processing files, please wait..." will be displayed
4. Processing time depends on file size (typically 10-60 seconds)

**Screenshot Location: Loading screen with spinner**

### Step 5: Review Results
Once processing is complete, the results panel will show:
- Success message with match statistics
- Download links for output files
- Detailed statistics breakdown

**Screenshot Location: Results panel with download links and statistics**

## Understanding Results

### Output Files

#### 1. Matched & Updated File
- **Filename**: `UNITED_ABRASIVES_ICSW_matched_updated_[timestamp].txt`
- **Contents**: All items from export file with updated prices
- **Use**: Import back into your system with new pricing

#### 2. Unmatched Items File
- **Filename**: `unmatched_only_[timestamp].txt`
- **Contents**: Items from export that couldn't be matched
- **Use**: Review for missing product codes or data issues

#### 3. Invalid Price Items File (if applicable)
- **Filename**: `invalid_prices_in_excel_[timestamp].txt`
- **Contents**: Price file entries with formatting issues
- **Use**: Fix data quality issues in your price file

#### 4. Items Not In Export File (if applicable)
- **Filename**: `not_in_export_[timestamp].txt`
- **Contents**: Price items not found in your export
- **Use**: Identify new products or discontinued items

**Screenshot Location: Download section with all file types**

### Statistics Panel

The statistics panel provides detailed insights:

#### Export File Statistics
- **Total Items**: Number of items in your export file
- **Matched Items**: Successfully price-matched items
- **Unmatched Items**: Items without price matches
- **Items Missing Product Code**: Items with empty vendprod field

#### Price File Statistics
- **Total Items**: Number of items in your price file
- **Invalid Price Items**: Items with formatting issues
- **Items Not In Export**: Price items not in your export

**Screenshot Location: Statistics panel with sample data**
