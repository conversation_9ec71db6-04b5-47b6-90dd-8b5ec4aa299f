# Drillmex Price Updater

A Node.js application for updating prices by matching export files (.txt) with price files (.xlsx).

## Project Structure

```
Drillmex/
├── app.js                             # Main application entry point
├── package.json                       # Project dependencies and scripts
├── README.md                          # Project documentation
├── start.bat                          # Windows launcher script
├── public/                            # Static files and generated reports
│   └── index.html                     # Web interface
├── uploads/                           # Temporary file uploads (auto-created)
└── src/                              # Source code
    ├── controllers/                   # Request handlers
    │   └── priceUpdateController.js   # Price update logic controller
    ├── services/                     # Business logic services
    │   ├── priceMatchingService.js   # Price matching algorithms
    │   └── reportService.js          # Report generation service
    └── utils/                        # Utility functions
        ├── directoryUtils.js         # Directory management utilities
        └── fileUtils.js              # File processing utilities
```

## Features

- **File Upload**: Secure handling of .txt export files and .xlsx price files
- **Price Matching**: Intelligent matching with exact and normalized matching algorithms
- **Report Generation**: Comprehensive reports including:
  - Complete export file with updated prices (all items included)
  - Unmatched items analysis
  - Invalid price items detection
  - Unused price items identification
  - Summary statistics
- **Error Handling**: Robust error handling and validation
- **Logging**: Detailed console logging for debugging and monitoring

## API Endpoints

### POST /update-prices
Processes export and price files to generate updated price data.

**Request:**
- `export_file`: .txt file (tab-separated values)
- `price_file`: .xlsx file with price data

**Response:**
```json
{
  "message": "✅ Price update completed...",
  "matchedFile": "complete_export_updated_2024-01-01T12-00-00-000Z.txt",
  "unmatchedFile": "unmatched_only_2024-01-01T12-00-00-000Z.txt",
  "summaryFile": "summary_report_2024-01-01T12-00-00-000Z.txt",
  "stats": {
    "totalItems": 1000,
    "matchedItems": 850,
    "matchPercentage": 85,
    ...
  }
}
```

### GET /api/status
Health check endpoint.

**Response:**
```json
{
  "status": "ok",
  "version": "2.0.0",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 📚 Documentation

### Quick Access Guides
- **[Quick Start Guide](QUICK_START_GUIDE.md)** - Get started in 5 minutes
- **[User Guide](USER_GUIDE.md)** - Complete documentation with detailed instructions
- **[Interface Guide](INTERFACE_GUIDE.md)** - Visual guide to the application interface
- **[Troubleshooting Guide](TROUBLESHOOTING_GUIDE.md)** - Solutions to common issues

## Installation & Usage

### Desktop Application (Recommended)

The application is available as a desktop application using Electron.js:

#### Quick Start (Windows)
1. Double-click `start.bat` to automatically install dependencies and launch the app

#### Manual Installation
1. Install dependencies:
```bash
npm install
```

2. Run the desktop application:
```bash
npm start
```

### Web Application (Alternative)

You can also access it as a web application:

1. Launch the desktop application using `start.bat`
2. Open http://localhost:3000 in your browser
3. Use the same interface in your web browser

## File Format Requirements

### Export File (.txt)
- Tab-separated values (TSV) format
- Must contain a `vendprod` column for product matching
- First row should contain column headers

### Price File (.xlsx)
- Excel format (.xlsx)
- Must contain columns: `Item #` (or variations) and `BASE PRICE` (or variations)
- The application automatically detects header rows

## Architecture Benefits

### Modular Design
- **Separation of Concerns**: Each module has a specific responsibility
- **Maintainability**: Easy to modify individual components without affecting others
- **Testability**: Each service and utility can be tested independently
- **Scalability**: Easy to add new features or modify existing ones

### Code Organization
- **Controllers**: Handle HTTP requests and responses
- **Services**: Contain business logic and complex operations
- **Utils**: Provide reusable utility functions

### Error Handling
- Proper cleanup of uploaded files on errors
- Detailed error messages and logging

### Performance
- Efficient file processing with streaming where possible
- Memory-conscious handling of large files
- Proper resource cleanup

## Environment Variables

- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Environment mode (development/production)

## Generated Files

All output files are saved in the `public/` directory with timestamps:
- `UNITED_ABRASIVES_ICSW_complete_export_updated_[timestamp].txt`: Complete export file with updated prices (all items included)
- `unmatched_only_[timestamp].txt`: Items that couldn't be matched
- `invalid_prices_in_excel_[timestamp].txt`: Price items with invalid data
- `not_in_export_[timestamp].txt`: Price items not found in export

## Version History

- **v2.0.0**: Complete refactor with modular architecture
- **v1.1.0**: Original monolithic implementation