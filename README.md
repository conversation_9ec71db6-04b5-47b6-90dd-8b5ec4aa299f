# Drillmex Web Application

A simple web application for price matching and data processing. Upload your export and price files to automatically match and update prices.

## Quick Start

### Windows Users (Recommended)
1. **Double-click `start.bat`**
2. **Wait for the browser to open automatically**
3. **Start uploading your files**

### Manual Start
1. Open command prompt in this folder
2. Run: `npm start`
3. Open browser to: `http://localhost:3000`

## Requirements

- **Node.js** (Download from [nodejs.org](https://nodejs.org/))
- **Modern web browser** (Chrome, Firefox, Edge, Safari)

## Features

- **Price Matching**: Upload and process Excel files for price matching
- **Column Selection**: Choose which columns to use for matching and updating
- **File Processing**: Handle export files and price files
- **Report Generation**: Generate detailed reports of matched items
- **Web Interface**: Clean, responsive web interface that works on any device
- **File Downloads**: Download processed results and reports

## How to Use

### Step 1: Prepare Your Files

**Export File (.txt)**
- Tab-separated values format
- Must contain a column with product codes (you'll select which one)
- Must contain a column for prices to be updated (you'll select which one)
- Example: Export from your inventory system

**Price File (.xlsx)**
- Excel format
- Must contain a column with product codes (you'll select which one)
- Must contain a column with new prices (you'll select which one)
- Example: Price list from supplier

### Step 2: Upload Files
1. Click "Choose File" for Export File
2. Select your .txt export file
3. Click "Choose File" for Price File
4. Select your .xlsx price file

### Step 3: Select Columns
After uploading both files, a column mapping section will appear:
1. **Infor File - Product Code Column**: Select the column containing product codes
2. **Infor File - Price Column to Update**: Select the column where new prices will be written
3. **Price File - Product Code Column**: Select the column containing product codes
4. **Price File - Price Column**: Select the column containing the new prices

### Step 4: Process Files
1. Click "Process and Update Prices" button
2. Wait for processing to complete
3. Review the results and statistics

### Step 5: Download Results
Click the download links to get:
- **Complete Export**: All items with updated prices
- **Additional Analysis**: Items not found in your export (if any)

## File Requirements

### Export File Format
```
vendprod    description    price    category
ABC123      Product A      10.50    Tools
XYZ789      Product B      25.00    Hardware
```

### Price File Format
| Item # | BASE PRICE | Description |
|--------|------------|-------------|
| ABC123 | 12.75      | Product A   |
| XYZ789 | 28.50      | Product B   |

## Understanding Results

### Statistics Panel
- **Total Items**: Number of items in your export file
- **Matched Items**: Successfully updated with new prices
- **Match Rate**: Percentage of successful matches

### Output Files
- **Complete Export**: Import this back into your system
- **Not in Export**: Price items not found in your export (if any)

## Troubleshooting

### Common Issues

**Application won't start:**
- Install Node.js from nodejs.org
- Run `npm install` in command prompt
- Try running `start.bat` as administrator

**Files won't upload:**
- Check file formats (.txt for export, .xlsx for price)
- Ensure files aren't too large (50MB limit)
- Verify required columns exist

**Low match rate:**
- Check product codes match between files
- Remove extra spaces or special characters
- Ensure vendprod column exists in export file

**Browser doesn't open:**
- Manually go to `http://localhost:3000`
- Check if port 3000 is available
- Try refreshing the page

### Error Messages

**"Export file missing required vendprod column"**
- Your export file needs a column named exactly `vendprod`

**"Price file missing required columns"**
- Your Excel file needs columns named `Item #` and `BASE PRICE`

**"File too large"**
- Files must be under 50MB
- Try splitting large files into smaller chunks

## Tips for Best Results

### File Preparation
- Keep product codes consistent across both files
- Remove empty rows from Excel files
- Use standard formats (no special formatting)

### Regular Use
- Process files in small batches for faster results
- Keep backup copies of original files
- Review unmatched items to improve data quality

### Data Quality
- Standardize product code formats
- Remove leading/trailing spaces
- Use consistent naming conventions

## Technical Details

- **Built with**: Node.js, Express.js, HTML/CSS/JavaScript
- **File Processing**: Handles TSV and Excel formats
- **Browser Support**: All modern browsers
- **File Limits**: 50MB per file
- **Concurrent Users**: Single user application

## File Structure

```
Drillmex/
├── start.bat          # Windows launcher
├── app.js             # Main application
├── package.json       # Dependencies
├── public/            # Web interface
│   └── index.html     # Main page
├── src/               # Application logic
│   ├── controllers/   # Request handlers
│   ├── services/      # Business logic
│   └── utils/         # Helper functions
└── uploads/           # Temporary file storage
```

## Getting Help

### Before Asking for Help
1. Check this README for solutions
2. Verify your files meet the format requirements
3. Try with smaller test files first

### Common Solutions
- Restart the application
- Check file formats and column names
- Ensure Node.js is properly installed
- Try using different browsers

## Version Information

- **Current Version**: Web Application (converted from desktop)
- **Last Updated**: June 2025
- **Compatibility**: Windows, macOS, Linux

---

**Need help?** Check the troubleshooting section above or verify your file formats match the requirements.
