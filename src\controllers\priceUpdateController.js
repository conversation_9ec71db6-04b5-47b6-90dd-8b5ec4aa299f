const { parseTSV } = require('../utils/fileUtils');
const { safeDeleteFile } = require('../utils/directoryUtils');
const PriceMatchingService = require('../services/priceMatchingService');
const ReportService = require('../services/reportService');

/**
 * Controller for handling price update operations
 */
class PriceUpdateController {
  
  /**
   * Handles the price update request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updatePrices(req, res) {
    // Validate file uploads
    if (!req.files || !req.files['export_file'] || !req.files['price_file']) {
      return res.status(400).json({ 
        error: 'Both export file and price file are required' 
      });
    }

    let exportFilePath = null;
    let priceFilePath = null;

    try {
      const exportFile = req.files['export_file'][0];
      const priceFile = req.files['price_file'][0];
      
      exportFilePath = exportFile.path;
      priceFilePath = priceFile.path;

      // Process export file
      console.log(`Processing export file: ${exportFile.originalname}`);
      const exportData = parseTSV(exportFilePath);
      console.log(`Found ${exportData.length} items in export file`);

      // Process price file
      const priceResults = PriceMatchingService.processPriceFile(priceFilePath);
      const { priceMap, validPriceItems, invalidPriceItems, priceData } = priceResults;

      // Log invalid price items if any
      PriceUpdateController.logInvalidPriceItems(invalidPriceItems);

      // Match prices
      const matchingResults = PriceMatchingService.matchPrices(exportData, priceMap, priceData);
      const { matchCount, itemsNotInExport } = matchingResults;

      // Log matching statistics
      PriceUpdateController.logMatchingStatistics(matchCount, exportData, matchingResults);

      // Generate reports
      const reportResults = ReportService.generateReports(
        matchingResults, 
        priceResults, 
        { exportFile, priceFile, exportData }
      );

      // Clean up uploaded files
      safeDeleteFile(exportFilePath);
      safeDeleteFile(priceFilePath);

      // Prepare response
      const responseFileNames = ReportService.getResponseFileNames(
        reportResults.filePaths,
        invalidPriceItems,
        itemsNotInExport
      );
      
      const matchPercentage = Math.round((matchCount / exportData.length) * 100);
      
      return res.json({
        message: `✅ Price update completed. Matched ${matchCount} out of ${exportData.length} items (${matchPercentage}%).`,
        ...responseFileNames,
        stats: reportResults.stats
      });

    } catch (err) {
      console.error('Error processing files:', err);
      
      // Clean up uploaded files in case of error
      if (exportFilePath) safeDeleteFile(exportFilePath);
      if (priceFilePath) safeDeleteFile(priceFilePath);
      
      return res.status(500).json({ error: err.message });
    }
  }

  /**
   * Logs invalid price items to console
   * @param {Array} invalidPriceItems - Array of invalid price items
   */
  static logInvalidPriceItems(invalidPriceItems) {
    if (invalidPriceItems.length > 0) {
      console.log(`\n===== INVALID PRICE ITEMS (${invalidPriceItems.length}) =====`);
      invalidPriceItems.slice(0, 20).forEach((item, index) => {
        if (item.itemNumber) {
          console.log(`${index + 1}. Item #: ${item.itemNumber}, Reason: ${item.reason}${item.price ? ', Value: ' + item.price : ''}`);
        } else {
          console.log(`${index + 1}. Row data: ${JSON.stringify(item.row).substring(0, 100)}..., Reason: ${item.reason}`);
        }
      });
      
      if (invalidPriceItems.length > 20) {
        console.log(`... and ${invalidPriceItems.length - 20} more items`);
      }
    }
  }

  /**
   * Logs matching statistics to console
   * @param {number} matchCount - Number of matched items
   * @param {Array} exportData - Export data array
   * @param {Object} matchingResults - Results from matching process
   */
  static logMatchingStatistics(matchCount, exportData, matchingResults) {
    const { emptyVendprodCount, itemsNotInExport } = matchingResults;
    const matchPercentage = Math.round((matchCount / exportData.length) * 100);
    
    console.log(`Matched ${matchCount} items out of ${exportData.length} (${matchPercentage}%)`);
    
    if (emptyVendprodCount > 0) {
      console.log(`Found ${emptyVendprodCount} items with missing vendor product codes`);
    }

    PriceUpdateController.logItemsNotInExport(itemsNotInExport);
  }

  /**
   * Logs items not in export to console
   * @param {Array} itemsNotInExport - Array of items not in export
   */
  static logItemsNotInExport(itemsNotInExport) {
    if (itemsNotInExport.length > 0) {
      console.log(`\n===== ITEMS IN PRICE FILE NOT FOUND IN EXPORT (${itemsNotInExport.length}) =====`);
      console.log(`These are price items that don't exist in your export file:`);
      
      itemsNotInExport.slice(0, 20).forEach((item, index) => {
        let itemInfo = `${index + 1}. Item #: ${item.itemNumber}, Price: ${item.price}`;
        if (item.description) {
          itemInfo += `, Description: ${item.description.substring(0, 30)}${item.description.length > 30 ? '...' : ''}`;
        }
        if (item.category) {
          itemInfo += `, Category: ${item.category}`;
        }
        console.log(itemInfo);
      });
      
      if (itemsNotInExport.length > 20) {
        console.log(`... and ${itemsNotInExport.length - 20} more items`);
      }
    }
  }
}

module.exports = PriceUpdateController;