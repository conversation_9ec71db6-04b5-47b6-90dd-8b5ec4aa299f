# Drillmex Quick Start Guide

## 🚀 Get Started in 5 Minutes

### Step 1: Launch Application
1. Double-click `start.bat` in the Drillmex folder
2. Wait for the desktop window to open (first run may take longer)

### Step 2: Prepare Your Files
You need two files:
- **Export File**: `.txt` file from SAAMM with `vendprod` column
- **Price File**: `.xlsx` Excel file with `Item #` and `BASE PRICE` columns

### Step 3: Upload Files
1. Click "Choose File" under "Export File from SAAMM (.txt)"
2. Select your export file
3. Click "Choose File" under "Price Excel File (.xlsx)"
4. Select your price file

### Step 4: Process
1. Click "Upload & Process Files"
2. Wait for processing to complete (spinner will show)

### Step 5: Download Results
1. Click download links for your processed files:
   - **Matched & Updated**: Main output file
   - **Unmatched**: Items that couldn't be matched
2. Review statistics for match percentage

## ✅ File Format Checklist

### Export File (.txt)
- [ ] Tab-separated format
- [ ] Contains `vendprod` column
- [ ] First row has column headers
- [ ] File size under 50MB

### Price File (.xlsx)
- [ ] Excel format (.xlsx, not .xls)
- [ ] Contains `Item #` column (or similar)
- [ ] Contains `BASE PRICE` column (or similar)
- [ ] No merged cells in data area
- [ ] File size under 50MB

## 🎯 Expected Results

### Good Match Rate (80%+)
- Most items will have updated prices
- Few unmatched items
- Ready to import back to your system

### Poor Match Rate (<60%)
- Check product code formats
- Review unmatched items file
- Verify column names in both files

## 🔧 Quick Troubleshooting

| Problem | Quick Fix |
|---------|-----------|
| App won't start | Run as administrator |
| File won't upload | Check file format and size |
| No matches | Verify product codes match |
| Processing stuck | Restart app and try smaller files |


