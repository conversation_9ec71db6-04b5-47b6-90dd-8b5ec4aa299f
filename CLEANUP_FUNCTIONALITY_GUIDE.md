# File Cleanup Functionality Guide

The Drillmex Web Application now includes comprehensive file cleanup functionality to help manage generated files and free up disk space.

## Overview

The cleanup functionality automatically manages generated files in the `public/` directory, including:
- Complete export files with updated prices
- Unmatched items reports
- Invalid price items reports
- Items not in export reports

## Features

### 1. Automatic File Detection
- Automatically identifies generated files based on naming patterns
- Protects important files (like `index.html`) from accidental deletion
- Recognizes files with timestamp patterns: `YYYY-MM-DDTHH-MM-SS-SSSZ`

### 2. File Statistics
- **Total Files**: Count of all generated files
- **Total Size**: Combined size of all generated files
- **File Distribution**: Breakdown by file type and age
- **Age Categories**: Today, This Week, This Month, Older

### 3. Cleanup Options

#### Refresh Information
- **Button**: "Refresh"
- **Function**: Updates file statistics and list
- **Use**: Get current status of generated files

#### Delete Old Files
- **Button**: "Delete Old Files (7+ days)"
- **Function**: Removes files older than 7 days
- **Safety**: Requires confirmation dialog
- **Use**: Regular maintenance to keep recent files

#### Delete All Generated Files
- **Button**: "Delete All Generated Files"
- **Function**: Removes all generated files
- **Safety**: Requires confirmation dialog with warning
- **Use**: Complete cleanup when starting fresh

## Web Interface

### Location
The cleanup section is located at the bottom of the main page, below the file upload and results sections.

### Layout
- **Left Panel**: Statistics and action buttons
- **Right Panel**: List of recent files with sizes

### Statistics Display
```
Total Generated Files: 15
Total Size: 2.3 MB
Files from Today: 3
Files from This Week: 8
Older Files: 4
```

### File List
Shows up to 10 most recent files with:
- Truncated filename (first 30 characters)
- File size in human-readable format
- Tooltip with full filename on hover

## API Endpoints

### GET /api/cleanup/info
Returns complete cleanup information including file list and statistics.

**Response:**
```json
{
  "success": true,
  "files": [
    {
      "name": "UNITED_ABRASIVES_ICSW_complete_export_updated_2025-06-27T10-30-15-123Z.txt",
      "type": "Complete Export (Updated)",
      "size": "1.2 MB",
      "sizeBytes": 1234567,
      "created": "2025-06-27T10:30:15.123Z",
      "createdFormatted": "6/27/2025, 10:30:15 AM"
    }
  ],
  "stats": {
    "totalFiles": 15,
    "totalSize": 2345678,
    "totalSizeFormatted": "2.3 MB",
    "filesByType": {
      "Complete Export (Updated)": 5,
      "Unmatched Items": 5,
      "Invalid Prices": 3,
      "Not in Export": 2
    },
    "filesByAge": {
      "today": 3,
      "thisWeek": 8,
      "thisMonth": 4,
      "older": 0
    }
  }
}
```

### GET /api/cleanup/stats
Returns only statistics without file list.

### POST /api/cleanup/old
Deletes files older than specified days.

**Request Body:**
```json
{
  "days": 7
}
```

**Response:**
```json
{
  "success": true,
  "deletedCount": 5,
  "deletedFiles": ["file1.txt", "file2.txt"],
  "errors": [],
  "message": "Deleted 5 files older than 7 days"
}
```

### POST /api/cleanup/all
Deletes all generated files.

**Response:**
```json
{
  "success": true,
  "deletedCount": 15,
  "deletedFiles": ["file1.txt", "file2.txt", "..."],
  "errors": [],
  "message": "Deleted 15 generated files"
}
```

### POST /api/cleanup/specific
Deletes specific files by name.

**Request Body:**
```json
{
  "fileNames": ["file1.txt", "file2.txt"]
}
```

## File Type Recognition

The system recognizes these file patterns:

1. **Complete Export Files**:
   - `UNITED_ABRASIVES_ICSW_complete_export_updated_[timestamp].txt`
   - `UNITED_ABRASIVES_ICSW_matched_updated_[timestamp].txt`

2. **Unmatched Items**:
   - `unmatched_only_[timestamp].txt`

3. **Invalid Prices**:
   - `invalid_prices_in_excel_[timestamp].txt`

4. **Not in Export**:
   - `not_in_export_[timestamp].txt`

## Security Features

- **Pattern Validation**: Only files matching generated patterns can be deleted
- **Path Protection**: Cannot delete files outside the public directory
- **Confirmation Dialogs**: All destructive operations require user confirmation
- **Error Handling**: Graceful handling of file system errors

## Best Practices

### Regular Maintenance
- Use "Delete Old Files" weekly to maintain reasonable disk usage
- Monitor file statistics to understand usage patterns
- Keep recent files for reference and troubleshooting

### Before Major Operations
- Use "Refresh" to get current status
- Review file list before bulk deletions
- Consider backing up important results before cleanup

### Storage Management
- Files older than 7 days are typically safe to delete
- Keep files from recent processing sessions for comparison
- Monitor total size to prevent disk space issues

## Troubleshooting

### Common Issues

**Cleanup buttons not working:**
- Check browser console for JavaScript errors
- Verify server is running and accessible
- Refresh the page and try again

**Files not being detected:**
- Ensure files follow the expected naming patterns
- Check file permissions in the public directory
- Verify files are actually in the public folder

**Deletion errors:**
- Check file permissions
- Ensure files are not locked by other processes
- Verify disk space is available

### Error Messages

- **"Not a generated file"**: File doesn't match expected patterns
- **"File not found"**: File was already deleted or moved
- **"Permission denied"**: Insufficient file system permissions

## Integration with Existing Workflow

The cleanup functionality integrates seamlessly with the existing price matching workflow:

1. **Upload and Process**: Files are generated normally
2. **Download Results**: Get your processed files
3. **Cleanup**: Use cleanup tools to manage old files
4. **Repeat**: Continue with new processing sessions

This ensures your system stays clean while preserving important functionality.
