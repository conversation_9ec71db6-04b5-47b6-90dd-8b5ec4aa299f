<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Drillmex</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22></text></svg>">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
      padding: 0;
      margin: 0;
      min-height: 100vh;
      font-size: 14px;
    }
    .app-container {
      margin: 10px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      padding: 15px;
      min-height: calc(100vh - 20px);
      display: flex;
      flex-direction: column;
    }
    
    /* Mobile responsiveness */
    @media (max-width: 768px) {
      .app-container {
        margin: 10px;
        padding: 15px;
        min-height: calc(100vh - 20px);
        border-radius: 5px;
      }
      
      .app-title {
        font-size: 1.25rem;
        margin-bottom: 10px;
        padding-bottom: 8px;
        text-align: center;
      }
      
      .content-row {
        padding-bottom: 15px;
      }
      
      .card {
        margin-bottom: 15px;
      }
      
      .card:last-child {
        margin-bottom: 0;
      }
      
      .card-header h5 {
        font-size: 1rem;
      }
      
      .card-body {
        padding: 15px;
      }
    }
    
    @media (max-width: 576px) {
      body {
        padding: 0;
      }
      
      .app-container {
        margin: 5px;
        padding: 10px;
        min-height: calc(100vh - 10px);
        border-radius: 0;
      }
      
      .app-title {
        font-size: 1.1rem;
        margin-bottom: 8px;
        padding-bottom: 6px;
      }
      
      .content-row {
        padding-bottom: 10px;
      }
      
      .card-body {
        padding: 10px;
      }
      
      .file-input-container {
        padding: 15px;
        margin-bottom: 15px;
      }
      
      .submit-btn {
        padding: 12px;
        font-size: 14px;
      }
      
      .download-link {
        display: block;
        margin: 6px 0;
        text-align: center;
        padding: 8px 12px;
        font-size: 0.8rem;
      }
         .stats-container {
      padding: 10px;
    }
    }
    .app-title {
      color: #0d6efd;
      margin-bottom: 10px;
      border-bottom: 1px solid #e9ecef;
      padding-bottom: 8px;
      flex-shrink: 0;
      font-size: 1.3rem;
    }
    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
    }
    .content-row {
      flex: 1;
      min-height: 0;
      padding-bottom: 10px;
    }
    .file-input-container {
      background-color: #f8f9fa;
      border: 1px dashed #ced4da;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 15px;
      transition: all 0.3s;
    }
    .file-input-container:hover {
      border-color: #0d6efd;
      background-color: #f1f8ff;
    }
    .file-input-label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      font-size: 14px;
    }
    .file-input {
      width: 100%;
    }
    .submit-btn {
      width: 100%;
      padding: 8px;
      font-size: 14px;
      margin-top: 8px;
    }
    .result-container {
      margin-top: 15px;
      padding: 15px;
      border-radius: 5px;
      display: none;
    }
    .result-container-inline {
      min-height: 150px;
      max-height: 100%;
      overflow-y: auto;
    }
    .success-result {
      background-color: #d1e7dd;
      border: 1px solid #badbcc;
      border-radius: 5px;
      padding: 12px;
    }
    .error-result {
      background-color: #f8d7da;
      border: 1px solid #f5c2c7;
      border-radius: 5px;
      padding: 12px;
    }
    .download-link {
      display: inline-block;
      margin: 4px 6px 4px 0;
      padding: 5px 10px;
      background-color: #0d6efd;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      transition: background-color 0.3s;
      font-size: 0.8rem;
    }
    .download-link:hover {
      background-color: #0b5ed7;
      color: white;
    }
    .stats-container {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 20px;
      margin-top: 15px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .stats-title {
      color: #0d6efd;
      font-weight: 600;
      margin-bottom: 15px;
      font-size: 16px;
      border-bottom: 2px solid #e9ecef;
      padding-bottom: 8px;
    }
    .stats-section {
      background: white;
      border-radius: 6px;
      padding: 15px;
      margin-bottom: 10px;
      border: 1px solid #e9ecef;
    }
    .stats-section p {
      color: #495057;
      font-weight: 500;
      margin-bottom: 12px;
      border-bottom: 1px solid #e9ecef;
      padding-bottom: 5px;
    }
    .stats-section ul {
      list-style: none;
      padding-left: 0;
      margin-bottom: 0;
    }
    .stats-section li {
      padding: 6px 0;
      color: #6c757d;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .stats-section li strong {
      color: #212529;
    }
    .stats-value {
      background: #e9ecef;
      padding: 2px 8px;
      border-radius: 4px;
      font-weight: 500;
    }
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      display: none;
    }
    .spinner-container {
      text-align: center;
    }
    .spinner-text {
      margin-top: 15px;
      font-weight: 500;
    }
    .file-format-info {
      font-size: 0.75rem;
      color: #6c757d;
      margin-top: 4px;
    }

    /* Cleanup Section Styles */
    .cleanup-stats {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 15px;
      font-size: 0.9rem;
    }

    .cleanup-stats .stat-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .cleanup-stats .stat-value {
      font-weight: bold;
      color: #0d6efd;
    }

    .cleanup-files-list {
      max-height: 200px;
      overflow-y: auto;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 10px;
    }

    .cleanup-file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 0;
      border-bottom: 1px solid #e9ecef;
      font-size: 0.8rem;
    }

    .cleanup-file-item:last-child {
      border-bottom: none;
    }

    .cleanup-file-name {
      flex: 1;
      margin-right: 10px;
      word-break: break-all;
    }

    .cleanup-file-size {
      color: #6c757d;
      white-space: nowrap;
    }

    .cleanup-actions .btn {
      margin-right: 5px;
      margin-bottom: 5px;
    }

    @media (max-width: 768px) {
      .cleanup-actions .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
      }

      .cleanup-actions .btn {
        margin-right: 0;
        margin-bottom: 8px;
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <h1 class="app-title">
      <i class="bi bi-file-earmark-spreadsheet"></i> Drillmex
    </h1>    
    <div class="main-content">
      <div class="row content-row">
      <!-- Upload Section -->
      <div class="col-lg-6 col-md-12">
        <div class="card h-100">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="bi bi-upload"></i> Input Files</h5>
          </div>
          <div class="card-body">
            <form id="uploadForm">
              <div class="file-input-container">
                <label class="file-input-label">Export File from SAAMM (.txt):</label>
                <input type="file" class="form-control file-input" name="export_file" accept=".txt" required>
                <div class="file-format-info">
                  Text file with "vendprod" column containing product codes
                </div>
              </div>
              
              <div class="file-input-container">
                <label class="file-input-label">Price Excel File (.xlsx):</label>
                <input type="file" class="form-control file-input" name="price_file" accept=".xlsx" required>
                <div class="file-format-info">
                  Excel file with "Item #" and "BASE PRICE" columns
                </div>
              </div>
              
              <button type="submit" class="btn btn-primary submit-btn">
                <i class="bi bi-upload"></i> Upload & Process Files
              </button>
            </form>
          </div>
        </div>
      </div>
      
      <!-- Results Section -->
      <div class="col-lg-6 col-md-12">
        <div class="card h-100">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="bi bi-download"></i>  Results</h5>
          </div>
          <div class="card-body">
            <div id="result" class="result-container-inline">
              <div class="text-muted text-center py-4">
                <i class="bi bi-file-earmark-arrow-down" style="font-size: 3rem;"></i>
                <p class="mt-2">After uploading files you can see results here</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>

      <!-- Cleanup Section -->
      <div class="row mt-3">
        <div class="col-12">
          <div class="card">
            <div class="card-header bg-warning text-dark">
              <h5 class="mb-0"><i class="bi bi-trash"></i> File Cleanup</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-lg-8 col-md-12">
                  <p class="mb-3">Manage generated files to free up disk space. Generated files include processed exports, unmatched items, and error reports.</p>

                  <div class="cleanup-stats" id="cleanupStats">
                    <div class="text-muted">Loading cleanup information...</div>
                  </div>

                  <div class="cleanup-actions mt-3">
                    <div class="btn-group" role="group">
                      <button type="button" class="btn btn-outline-info btn-sm" onclick="refreshCleanupInfo()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                      </button>
                      <button type="button" class="btn btn-outline-warning btn-sm" onclick="cleanupOldFiles()">
                        <i class="bi bi-calendar-x"></i> Delete Old Files (7+ days)
                      </button>
                      <button type="button" class="btn btn-outline-danger btn-sm" onclick="cleanupAllFiles()">
                        <i class="bi bi-trash"></i> Delete All Generated Files
                      </button>
                    </div>
                  </div>
                </div>

                <div class="col-lg-4 col-md-12">
                  <div class="cleanup-files-list" id="cleanupFilesList">
                    <div class="text-muted">Loading files...</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="loading-overlay" id="loadingOverlay">
    <div class="spinner-container">
      <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <div class="spinner-text">Processing files, please wait...</div>
    </div>
  </div>

  <script>
    document.getElementById('uploadForm').addEventListener('submit', async function (e) {
      e.preventDefault();
      
      // Show loading overlay
      document.getElementById('loadingOverlay').style.display = 'flex';
      
      const formData = new FormData(this);
      const resultDiv = document.getElementById('result');
      
      try {
        const res = await fetch('/update-prices', {
          method: 'POST',
          body: formData
        });
        
        const data = await res.json();
        
        // Hide loading overlay
        document.getElementById('loadingOverlay').style.display = 'none';
        
        // Clear previous results
        resultDiv.innerHTML = '';
        resultDiv.className = 'result-container-inline';
        
        if (res.ok) {
          // Success response
          resultDiv.classList.add('success-result');
          
          let statsHtml = '';
          if (data.stats) {
            statsHtml = `
              <div class="stats-container">
                <div class="stats-title">Statistics</div>
                <div class="row">
                  <div class="col-lg-6 col-md-12">
                    <div class="stats-section">
                      <p><strong>Export File Statistics</strong></p>
                      <ul>
                        <li>Total Items <span class="stats-value">${data.stats.totalItems}</span></li>
                        <li>Matched Items <span class="stats-value">${data.stats.matchedItems}</span></li>
                        <li>Unmatched Items <span class="stats-value">${data.stats.unmatchedItems}</span></li>
                        ${data.stats.emptyVendprodCount > 0 ? 
                          `<li>Items Missing Product Code <span class="stats-value">${data.stats.emptyVendprodCount}</span></li>` : ''}
                      </ul>
                    </div>
                  </div>
                  <div class="col-lg-6 col-md-12">
                    <div class="stats-section">
                      <p><strong>Price File Statistics</strong></p>
                      <ul>
                        <li>Total Items <span class="stats-value">${data.stats.priceItems + (data.stats.invalidPriceItems || 0)}</span></li>
                        ${data.stats.invalidPriceItems > 0 ? 
                          `<li>Invalid Price Items In Price File <span class="stats-value">${data.stats.invalidPriceItems}</span></li>` : ''}
                        ${data.stats.itemsNotInExport > 0 ? 
                          `<li>Items Not In Export File <span class="stats-value">${data.stats.itemsNotInExport}</span></li>` : ''}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            `;
          }
          
          resultDiv.innerHTML = `
            <div class="mb-3">
              <h5>Output Files:</h5>
              <a href="/${data.matchedFile}" class="download-link" download>
                <i class="bi bi-download"></i> Download Complete Export with Updated Prices
              </a>
              <a href="/${data.unmatchedFile}" class="download-link" download>
                <i class="bi bi-download"></i> Download Unmatched
              </a>
            </div>
            
            ${data.invalidPricesFile || data.notInExportFile ? `
              <div class="mb-3">
                <h5>Additional Analysis:</h5>
                ${data.invalidPricesFile ? `
                  <a href="/${data.invalidPricesFile}" class="download-link" download>
                    <i class="bi bi-download"></i> Invalid Price Items In Price File
                  </a>
                ` : ''}
                ${data.notInExportFile ? `
                  <a href="/${data.notInExportFile}" class="download-link" download>
                    <i class="bi bi-download"></i> Items Present In Price File But Not In Export File
                  </a>
                ` : ''}
              </div>
            ` : ''}
            
            ${statsHtml}
          `;
        } else {
          // Error response
          resultDiv.classList.add('error-result');
          resultDiv.innerHTML = `
            <h4><i class="bi bi-exclamation-triangle-fill"></i> Error</h4>
            <p>${data.error || 'An unknown error occurred'}</p>
          `;
        }
      } catch (err) {
        // Hide loading overlay
        document.getElementById('loadingOverlay').style.display = 'none';
        
        // Show error
        resultDiv.className = 'result-container-inline error-result';
        resultDiv.innerHTML = `
          <h4><i class="bi bi-exclamation-triangle-fill"></i> Error</h4>
          <p>Failed to process files: ${err.message}</p>
        `;
      }
    });
    
    // Check server status on page load
    async function checkServerStatus() {
      try {
        const res = await fetch('/api/status');
        if (res.ok) {
          console.log('Server is running properly');
        }
      } catch (err) {
        console.error('Server status check failed:', err);
      }
    }
    
    // Run status check when page loads
    window.addEventListener('load', checkServerStatus);

    // Cleanup functionality
    async function refreshCleanupInfo() {
      try {
        const response = await fetch('/api/cleanup/info');
        const data = await response.json();

        if (data.success) {
          updateCleanupDisplay(data);
        } else {
          document.getElementById('cleanupStats').innerHTML =
            '<div class="text-danger">Error loading cleanup information</div>';
        }
      } catch (error) {
        console.error('Error fetching cleanup info:', error);
        document.getElementById('cleanupStats').innerHTML =
          '<div class="text-danger">Failed to load cleanup information</div>';
      }
    }

    function updateCleanupDisplay(data) {
      // Update stats
      const statsHtml = `
        <div class="stat-item">
          <span>Total Generated Files:</span>
          <span class="stat-value">${data.stats.totalFiles}</span>
        </div>
        <div class="stat-item">
          <span>Total Size:</span>
          <span class="stat-value">${data.stats.totalSizeFormatted}</span>
        </div>
        <div class="stat-item">
          <span>Files from Today:</span>
          <span class="stat-value">${data.stats.filesByAge.today}</span>
        </div>
        <div class="stat-item">
          <span>Files from This Week:</span>
          <span class="stat-value">${data.stats.filesByAge.thisWeek}</span>
        </div>
        <div class="stat-item">
          <span>Older Files:</span>
          <span class="stat-value">${data.stats.filesByAge.older}</span>
        </div>
      `;
      document.getElementById('cleanupStats').innerHTML = statsHtml;

      // Update files list
      const filesHtml = data.files.length > 0 ?
        data.files.slice(0, 10).map(file => `
          <div class="cleanup-file-item">
            <div class="cleanup-file-name" title="${file.name}">${file.name.substring(0, 30)}${file.name.length > 30 ? '...' : ''}</div>
            <div class="cleanup-file-size">${file.size}</div>
          </div>
        `).join('') :
        '<div class="text-muted">No generated files found</div>';

      document.getElementById('cleanupFilesList').innerHTML =
        `<div style="font-weight: bold; margin-bottom: 10px;">Recent Files:</div>${filesHtml}`;
    }

    async function cleanupOldFiles() {
      if (!confirm('Delete all generated files older than 7 days?')) {
        return;
      }

      try {
        const response = await fetch('/api/cleanup/old', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ days: 7 })
        });

        const data = await response.json();

        if (data.success) {
          alert(`Successfully deleted ${data.deletedCount} old files`);
          refreshCleanupInfo();
        } else {
          alert('Error: ' + data.error);
        }
      } catch (error) {
        console.error('Error cleaning up old files:', error);
        alert('Failed to cleanup old files');
      }
    }

    async function cleanupAllFiles() {
      if (!confirm('Delete ALL generated files? This action cannot be undone!')) {
        return;
      }

      try {
        const response = await fetch('/api/cleanup/all', {
          method: 'POST'
        });

        const data = await response.json();

        if (data.success) {
          alert(`Successfully deleted ${data.deletedCount} files`);
          refreshCleanupInfo();
        } else {
          alert('Error: ' + data.error);
        }
      } catch (error) {
        console.error('Error cleaning up all files:', error);
        alert('Failed to cleanup all files');
      }
    }

    // Load cleanup info when page loads
    window.addEventListener('load', refreshCleanupInfo);
  </script>
  
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</body>
</html>