# Drillmex Interface Guide

## Main Application Window

When you launch Drillmex, you'll see a desktop application window with the following layout:

### Window Header
```
┌─────────────────────────────────────────────────────────────┐
│ 🗂️ Drillmex                                    ⚊ ⬜ ✕      │
└─────────────────────────────────────────────────────────────┘
```
- **Title**: "Drillmex" with file icon
- **Window Controls**: Minimize, maximize, close buttons

### Main Content Area

The application is divided into two main columns:

```
┌─────────────────────────────────────────────────────────────┐
│                        🗂️ Drillmex                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────┐    ┌─────────────────────┐        │
│  │    📤 Input Files    │    │   📥 Results        │        │
│  │                     │    │                     │        │
│  │  Export File Upload │    │  Download Links     │        │
│  │  Price File Upload  │    │  Statistics         │        │
│  │  Process Button     │    │  Status Messages    │        │
│  │                     │    │                     │        │
│  └─────────────────────┘    └─────────────────────┘        │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Left Panel: Input Files

### Export File Section
```
┌─────────────────────────────────────────────────────────────┐
│ Export File from SAAMM (.txt):                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Choose File                              No file chosen │ │
│ └─────────────────────────────────────────────────────────┘ │
│ Text file with "vendprod" column containing product codes  │
└─────────────────────────────────────────────────────────────┘
```

**Elements:**
- **Label**: "Export File from SAAMM (.txt):"
- **File Input**: Button showing "Choose File" or selected filename
- **Help Text**: Format requirements description

### Price File Section
```
┌─────────────────────────────────────────────────────────────┐
│ Price Excel File (.xlsx):                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Choose File                              No file chosen │ │
│ └─────────────────────────────────────────────────────────┘ │
│ Excel file with "Item #" and "BASE PRICE" columns         │
└─────────────────────────────────────────────────────────────┘
```

**Elements:**
- **Label**: "Price Excel File (.xlsx):"
- **File Input**: Button showing "Choose File" or selected filename
- **Help Text**: Required columns description

### Process Button
```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│        ┌─────────────────────────────────────────┐          │
│        │  📤 Upload & Process Files              │          │
│        └─────────────────────────────────────────┘          │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

**States:**
- **Enabled**: Blue button when both files selected
- **Disabled**: Gray button when files missing
- **Processing**: Shows loading state during processing

## Right Panel: Results

### Initial State (No Processing)
```
┌─────────────────────────────────────────────────────────────┐
│                     📥 Results                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                    📄                                       │
│                                                             │
│         After uploading files you can                      │
│              see results here                               │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Processing State
```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                       ⟳                                    │
│                                                             │
│              Processing files, please wait...               │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

**Elements:**
- **Spinner**: Animated loading indicator
- **Message**: "Processing files, please wait..."
- **Overlay**: Semi-transparent background

### Success Results
```
┌─────────────────────────────────────────────────────────────┐
│                     📥 Results                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Output Files:                                               │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 📥 Download Matched & Updated                           │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 📥 Download Unmatched                                   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Additional Analysis:                                        │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 📥 Invalid Price Items In Price File                   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Statistics Panel
```
┌─────────────────────────────────────────────────────────────┐
│                      Statistics                            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Export File Statistics    │  Price File Statistics         │
│ ┌─────────────────────────┐ ┌─────────────────────────────┐ │
│ │ Total Items        1000 │ │ Total Items        1200     │ │
│ │ Matched Items       850 │ │ Invalid Price Items   5     │ │
│ │ Unmatched Items     150 │ │ Items Not In Export  200    │ │
│ │ Missing Product Code 10 │ │                             │ │
│ └─────────────────────────┘ └─────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Error State
```
┌─────────────────────────────────────────────────────────────┐
│                     📥 Results                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                    ⚠️ Error                                 │
│                                                             │
│              Error message details here                     │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Color Coding

### Status Colors
- **Blue**: Primary actions and headers
- **Green**: Success states and positive statistics
- **Red**: Errors and warnings
- **Gray**: Disabled states and secondary text
- **Yellow**: Warnings and attention items

### File Type Icons
- **📤**: Upload/Input actions
- **📥**: Download/Output actions
- **📄**: Document/File representations
- **⚠️**: Warning/Error states
- **⟳**: Processing/Loading states

## Responsive Design

The interface adapts to different window sizes:

### Desktop View (1200px+)
- Two-column layout
- Full statistics panel
- Large buttons and text

### Tablet View (768px-1199px)
- Stacked layout
- Condensed statistics
- Medium-sized elements

### Mobile View (<768px)
- Single column
- Simplified statistics
- Touch-friendly buttons

## Accessibility Features

- **High Contrast**: Clear visual separation
- **Large Click Targets**: Easy to click buttons
- **Clear Typography**: Readable fonts and sizes
- **Status Messages**: Clear feedback for all actions
- **Keyboard Navigation**: Tab through all interactive elements

## Window Management

### Resizing
- **Minimum Size**: 800x600 pixels
- **Maximum Size**: Full screen
- **Responsive**: Content adapts to window size

### States
- **Normal**: Standard windowed mode
- **Maximized**: Full screen with title bar
- **Minimized**: Hidden to taskbar

---
**Interface Version**: 2.0.0 | **Last Updated**: December 2024
