const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const net = require('net');

// Import your existing controllers
const priceUpdateController = require('./src/controllers/priceUpdateController');

let server;
let serverPort = 3000;

// Function to find an available port
function findAvailablePort(startPort) {
  return new Promise((resolve, reject) => {
    const server = net.createServer();
    server.listen(startPort, (err) => {
      if (err) {
        server.close();
        findAvailablePort(startPort + 1).then(resolve).catch(reject);
      } else {
        const port = server.address().port;
        server.close();
        resolve(port);
      }
    });
  });
}

// Create Express server
function createServer() {
  const expressApp = express();
  
  // Middleware
  expressApp.use(cors());
  expressApp.use(express.static(path.join(__dirname, 'public')));
  expressApp.use('/uploads', express.static(path.join(__dirname, 'uploads')));
  
  // Ensure uploads directory exists
  const uploadsDir = path.join(__dirname, 'uploads');
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }
  
  // Configure multer for file uploads
  const upload = multer({ 
    dest: uploadsDir,
    limits: { fileSize: 50 * 1024 * 1024 } // 50MB limit
  });
  
  // Routes
  expressApp.post('/update-prices', upload.fields([
    { name: 'export_file', maxCount: 1 },
    { name: 'price_file', maxCount: 1 }
  ]), priceUpdateController.updatePrices);
  
  expressApp.get('/api/status', (req, res) => {
    res.json({ status: 'ok', message: 'Drillmex Web Application is running' });
  });
  
  return expressApp;
}

// Function to open browser automatically (optional)
function openBrowser(url) {
  const start = (process.platform === 'darwin' ? 'open' :
                process.platform === 'win32' ? 'start' : 'xdg-open');
  require('child_process').exec(`${start} ${url}`);
}

// Start the application
async function startApplication() {
  try {
    // Find an available port
    serverPort = await findAvailablePort(3000);
    console.log(`Using port: ${serverPort}`);

    // Start the Express server
    const expressApp = createServer();
    server = expressApp.listen(serverPort, 'localhost', (err) => {
      if (err) {
        console.error('Failed to start server:', err);
        process.exit(1);
      }

      console.log(`\n========================================`);
      console.log(`    Drillmex Web Application Started`);
      console.log(`========================================`);
      console.log(`Server running on: http://localhost:${serverPort}`);
      console.log(`\nOpen your web browser and navigate to:`);
      console.log(`http://localhost:${serverPort}`);
      console.log(`\nPress Ctrl+C to stop the server`);
      console.log(`========================================\n`);

      // Optionally open browser automatically
      if (process.env.AUTO_OPEN_BROWSER !== 'false') {
        setTimeout(() => {
          openBrowser(`http://localhost:${serverPort}`);
        }, 1000);
      }
    });

    // Handle server errors
    server.on('error', (err) => {
      console.error('Server error:', err);
      process.exit(1);
    });

  } catch (error) {
    console.error('Failed to start application:', error);
    process.exit(1);
  }
}

// Start the application
startApplication();

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\nShutting down gracefully...');
  if (server) {
    server.close(() => {
      console.log('Server closed');
      process.exit(0);
    });
  } else {
    process.exit(0);
  }
});

process.on('SIGTERM', () => {
  console.log('\n\nShutting down gracefully...');
  if (server) {
    server.close(() => {
      console.log('Server closed');
      process.exit(0);
    });
  } else {
    process.exit(0);
  }
});