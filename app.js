const { app, BrowserWindow, dialog } = require('electron');
const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const net = require('net');

// Import your existing controllers
const priceUpdateController = require('./src/controllers/priceUpdateController');

let mainWindow;
let server;
let serverPort = 3000;

// Function to find an available port
function findAvailablePort(startPort) {
  return new Promise((resolve, reject) => {
    const server = net.createServer();
    server.listen(startPort, (err) => {
      if (err) {
        server.close();
        findAvailablePort(startPort + 1).then(resolve).catch(reject);
      } else {
        const port = server.address().port;
        server.close();
        resolve(port);
      }
    });
  });
}

// Create Express server
function createServer() {
  const expressApp = express();
  
  // Middleware
  expressApp.use(cors());
  expressApp.use(express.static(path.join(__dirname, 'public')));
  expressApp.use('/uploads', express.static(path.join(__dirname, 'uploads')));
  
  // Ensure uploads directory exists
  const uploadsDir = path.join(__dirname, 'uploads');
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }
  
  // Configure multer for file uploads
  const upload = multer({ 
    dest: uploadsDir,
    limits: { fileSize: 50 * 1024 * 1024 } // 50MB limit
  });
  
  // Routes
  expressApp.post('/update-prices', upload.fields([
    { name: 'export_file', maxCount: 1 },
    { name: 'price_file', maxCount: 1 }
  ]), priceUpdateController.updatePrices);
  
  expressApp.get('/api/status', (req, res) => {
    res.json({ status: 'ok', message: 'Drillmex Desktop is running' });
  });
  
  return expressApp;
}

// Create the main window
function createWindow(port) {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true
    },
    show: false,
    icon: path.join(__dirname, 'assets', 'icon.png'), // Add icon if available
    title: 'Drillmex Desktop Application'
  });

  // Load the app
  mainWindow.loadURL(`http://localhost:${port}`);

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    console.log('Drillmex Desktop Application started successfully!');
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle navigation to external URLs
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });

  // Development tools (remove in production)
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
}

// Start the application
app.whenReady().then(async () => {
  try {
    // Find an available port
    serverPort = await findAvailablePort(3000);
    console.log(`Using port: ${serverPort}`);
    
    // Start the Express server
    const expressApp = createServer();
    server = expressApp.listen(serverPort, 'localhost', (err) => {
      if (err) {
        console.error('Failed to start server:', err);
        dialog.showErrorBox('Server Error', 'Failed to start the application server. Please try again.');
        app.quit();
        return;
      }
      
      console.log(`Server running on http://localhost:${serverPort}`);
      
      // Create the main window after server starts
      createWindow(serverPort);
    });
    
    // Handle server errors
    server.on('error', (err) => {
      console.error('Server error:', err);
      dialog.showErrorBox('Server Error', `Server encountered an error: ${err.message}`);
    });
    
  } catch (error) {
    console.error('Failed to start application:', error);
    dialog.showErrorBox('Startup Error', `Failed to start application: ${error.message}`);
    app.quit();
  }
});

// App event handlers
app.on('window-all-closed', () => {
  if (server) {
    server.close(() => {
      console.log('Server closed');
    });
  }
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow(serverPort);
  }
});

app.on('before-quit', () => {
  if (server) {
    server.close();
  }
});

// Handle certificate errors (for development)
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (url.startsWith('http://localhost:')) {
    // Ignore certificate errors for localhost
    event.preventDefault();
    callback(true);
  } else {
    callback(false);
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    require('electron').shell.openExternal(navigationUrl);
  });
});