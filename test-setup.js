 -#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Testing Drillmex Desktop Application Setup...\n');

// Test 1: Check required files
const requiredFiles = [
  'app.js',
  'package.json',
  'public/index.html',
  'src/controllers/priceUpdateController.js',
  'src/services/priceMatchingService.js',
  'src/services/reportService.js',
  'src/utils/fileUtils.js',
  'src/utils/directoryUtils.js'
];

console.log('📁 Checking required files...');
let missingFiles = [];
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - MISSING`);
    missingFiles.push(file);
  }
});

// Test 2: Check directories
const requiredDirs = ['src', 'public', 'src/controllers', 'src/services', 'src/utils'];
console.log('\n📂 Checking directories...');
requiredDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`  ✅ ${dir}/`);
  } else {
    console.log(`  ❌ ${dir}/ - MISSING`);
  }
});

// Test 3: Check package.json
console.log('\n📦 Checking package.json...');
try {
  const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  console.log(`  ✅ Name: ${pkg.name}`);
  console.log(`  ✅ Version: ${pkg.version}`);
  console.log(`  ✅ Main: ${pkg.main}`);
  
  const requiredDeps = ['electron', 'express', 'multer', 'cors', 'xlsx'];
  console.log('  📋 Dependencies:');
  requiredDeps.forEach(dep => {
    if (pkg.dependencies && pkg.dependencies[dep]) {
      console.log(`    ✅ ${dep}: ${pkg.dependencies[dep]}`);
    } else {
      console.log(`    ❌ ${dep} - MISSING`);
    }
  });
} catch (error) {
  console.log(`  ❌ Error reading package.json: ${error.message}`);
}

// Test 4: Check Node.js modules
console.log('\n🔧 Testing Node.js modules...');
const testModules = ['path', 'fs', 'express', 'electron'];
testModules.forEach(module => {
  try {
    if (module === 'electron') {
      // Special handling for electron in non-electron environment
      const electronPath = path.join('node_modules', 'electron');
      if (fs.existsSync(electronPath)) {
        console.log(`  ✅ ${module} (installed)`);
      } else {
        console.log(`  ❌ ${module} - not installed`);
      }
    } else {
      require(module);
      console.log(`  ✅ ${module}`);
    }
  } catch (error) {
    console.log(`  ❌ ${module} - ${error.message}`);
  }
});

// Test 5: Check app.js syntax
console.log('\n⚙️  Testing app.js syntax...');
try {
  const appContent = fs.readFileSync('app.js', 'utf8');
  // Basic syntax check
  if (appContent.includes('require(\'electron\')')) {
    console.log('  ✅ Electron import found');
  }
  if (appContent.includes('BrowserWindow')) {
    console.log('  ✅ BrowserWindow usage found');
  }
  if (appContent.includes('express()')) {
    console.log('  ✅ Express server setup found');
  }
  if (appContent.includes('app.whenReady')) {
    console.log('  ✅ Electron app ready handler found');
  }
} catch (error) {
  console.log(`  ❌ Error reading app.js: ${error.message}`);
}

// Summary
console.log('\n📊 Setup Summary:');
if (missingFiles.length === 0) {
  console.log('  ✅ All required files present');
  console.log('  🚀 Application should be ready to run!');
  console.log('\n💡 To start the application:');
  console.log('     Windows: Double-click start.bat');
  console.log('     macOS/Linux: ./start.sh');
  console.log('     Manual: npm start');
} else {
  console.log(`  ❌ ${missingFiles.length} files missing`);
  console.log('  🔧 Please ensure all files are present before running');
}

console.log('\n🔍 Test completed!');