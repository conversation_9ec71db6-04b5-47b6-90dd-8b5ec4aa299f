# Drillmex Web Application

This is a simple web application for price matching and data processing, converted from a desktop Electron application to a lightweight Node.js web server.

## Quick Start

### Option 1: Using the Batch File (Windows)
1. Double-click `start.bat`
2. The application will automatically start and open in your web browser
3. Keep the command window open while using the application
4. Press `Ctrl+C` in the command window to stop the server

### Option 2: Using Command Line
1. Open a command prompt or terminal in the application directory
2. Run: `npm start`
3. Open your web browser and navigate to `http://localhost:3000`
4. Press `Ctrl+C` to stop the server

## Requirements

- **Node.js** (version 14 or higher)
- **npm** (comes with Node.js)
- A modern web browser (Chrome, Firefox, Edge, Safari)

## Installation

If this is your first time running the application:

1. Make sure Node.js is installed on your system
2. Open a command prompt in the application directory
3. Run: `npm install` (this will install all required dependencies)
4. Run: `npm start` or use `start.bat`

## Features

- **Price Matching**: Upload and process Excel files for price matching
- **File Processing**: Handle export files and price files
- **Report Generation**: Generate detailed reports of matched and unmatched items
- **Web Interface**: Clean, responsive web interface that works on any device
- **File Downloads**: Download processed results and reports
- **File Cleanup**: Manage generated files with automatic cleanup tools

## Usage

1. Start the application using one of the methods above
2. Open your web browser to `http://localhost:3000`
3. Upload your export file and price file using the web interface
4. Click "Update Prices" to process the files
5. Download the generated reports and updated files
6. Use the cleanup section to manage old generated files

## Stopping the Application

- Press `Ctrl+C` in the command window where the server is running
- Or simply close the command window

## Troubleshooting

### Port Already in Use
If port 3000 is already in use, the application will automatically find the next available port and display the correct URL.

### Browser Doesn't Open Automatically
If the browser doesn't open automatically, manually navigate to the URL shown in the command window (usually `http://localhost:3000`).

### Dependencies Issues
If you encounter dependency issues, try:
```bash
npm install
```

## File Structure

- `app.js` - Main application server
- `start.bat` - Windows batch file to start the application
- `public/` - Web interface files (HTML, CSS, JavaScript)
- `src/` - Application logic (controllers, services, utilities)
- `uploads/` - Temporary file storage for uploaded files
- `package.json` - Node.js dependencies and scripts

## Differences from Desktop Version

- No longer requires Electron
- Runs as a web application in your browser
- Lighter weight and faster startup
- Can be accessed from any device on the same network
- Easier to deploy and maintain

## Network Access

By default, the application only accepts connections from localhost (127.0.0.1). To allow access from other devices on your network, you would need to modify the server configuration in `app.js`.
