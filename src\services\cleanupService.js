const fs = require('fs');
const path = require('path');

/**
 * Service class for cleaning up generated files in the public directory
 */
class CleanupService {
  
  /**
   * Gets the public directory path
   * @returns {string} Public directory path
   */
  static getPublicDir() {
    return path.join(process.cwd(), 'public');
  }
  
  /**
   * Gets all generated files in the public directory
   * @returns {Array} Array of file objects with name, path, and stats
   */
  static getGeneratedFiles() {
    const publicDir = this.getPublicDir();
    const files = [];
    
    try {
      const dirContents = fs.readdirSync(publicDir);
      
      for (const fileName of dirContents) {
        // Skip index.html and other non-generated files
        if (fileName === 'index.html' || !this.isGeneratedFile(fileName)) {
          continue;
        }
        
        const filePath = path.join(publicDir, fileName);
        const stats = fs.statSync(filePath);
        
        files.push({
          name: fileName,
          path: filePath,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          type: this.getFileType(fileName)
        });
      }
      
      // Sort by creation date (newest first)
      files.sort((a, b) => b.created - a.created);
      
    } catch (error) {
      console.error('Error reading public directory:', error);
    }
    
    return files;
  }
  
  /**
   * Determines if a file is a generated file based on naming patterns
   * @param {string} fileName - Name of the file
   * @returns {boolean} True if it's a generated file
   */
  static isGeneratedFile(fileName) {
    const generatedPatterns = [
      /^UNITED_ABRASIVES_ICSW_complete_export_updated_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z\.txt$/,
      /^UNITED_ABRASIVES_ICSW_matched_updated_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z\.txt$/,
      /^unmatched_only_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z\.txt$/,
      /^invalid_prices_in_excel_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z\.txt$/,
      /^not_in_export_\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}-\d{3}Z\.txt$/
    ];
    
    return generatedPatterns.some(pattern => pattern.test(fileName));
  }
  
  /**
   * Gets the file type based on filename pattern
   * @param {string} fileName - Name of the file
   * @returns {string} File type description
   */
  static getFileType(fileName) {
    if (fileName.includes('complete_export_updated') || fileName.includes('matched_updated')) {
      return 'Complete Export (Updated)';
    } else if (fileName.includes('unmatched_only')) {
      return 'Unmatched Items';
    } else if (fileName.includes('invalid_prices_in_excel')) {
      return 'Invalid Prices';
    } else if (fileName.includes('not_in_export')) {
      return 'Not in Export';
    }
    return 'Generated File';
  }
  
  /**
   * Deletes files older than specified days
   * @param {number} days - Number of days (files older than this will be deleted)
   * @returns {Object} Cleanup results
   */
  static cleanupOldFiles(days = 7) {
    const files = this.getGeneratedFiles();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    const filesToDelete = files.filter(file => file.created < cutoffDate);
    const deletedFiles = [];
    const errors = [];
    
    for (const file of filesToDelete) {
      try {
        fs.unlinkSync(file.path);
        deletedFiles.push(file.name);
        console.log(`Deleted old file: ${file.name}`);
      } catch (error) {
        errors.push({ file: file.name, error: error.message });
        console.error(`Error deleting file ${file.name}:`, error);
      }
    }
    
    return {
      success: true,
      deletedCount: deletedFiles.length,
      deletedFiles,
      errors,
      message: `Deleted ${deletedFiles.length} files older than ${days} days`
    };
  }
  
  /**
   * Deletes all generated files
   * @returns {Object} Cleanup results
   */
  static cleanupAllFiles() {
    const files = this.getGeneratedFiles();
    const deletedFiles = [];
    const errors = [];
    
    for (const file of files) {
      try {
        fs.unlinkSync(file.path);
        deletedFiles.push(file.name);
        console.log(`Deleted file: ${file.name}`);
      } catch (error) {
        errors.push({ file: file.name, error: error.message });
        console.error(`Error deleting file ${file.name}:`, error);
      }
    }
    
    return {
      success: true,
      deletedCount: deletedFiles.length,
      deletedFiles,
      errors,
      message: `Deleted ${deletedFiles.length} generated files`
    };
  }
  
  /**
   * Deletes specific files by name
   * @param {Array} fileNames - Array of file names to delete
   * @returns {Object} Cleanup results
   */
  static deleteSpecificFiles(fileNames) {
    const deletedFiles = [];
    const errors = [];
    const publicDir = this.getPublicDir();
    
    for (const fileName of fileNames) {
      // Security check - only allow deletion of generated files
      if (!this.isGeneratedFile(fileName)) {
        errors.push({ file: fileName, error: 'Not a generated file - deletion not allowed' });
        continue;
      }
      
      const filePath = path.join(publicDir, fileName);
      
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          deletedFiles.push(fileName);
          console.log(`Deleted file: ${fileName}`);
        } else {
          errors.push({ file: fileName, error: 'File not found' });
        }
      } catch (error) {
        errors.push({ file: fileName, error: error.message });
        console.error(`Error deleting file ${fileName}:`, error);
      }
    }
    
    return {
      success: true,
      deletedCount: deletedFiles.length,
      deletedFiles,
      errors,
      message: `Deleted ${deletedFiles.length} of ${fileNames.length} requested files`
    };
  }
  
  /**
   * Gets cleanup statistics
   * @returns {Object} Statistics about generated files
   */
  static getCleanupStats() {
    const files = this.getGeneratedFiles();
    const now = new Date();
    
    const stats = {
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      filesByType: {},
      filesByAge: {
        today: 0,
        thisWeek: 0,
        thisMonth: 0,
        older: 0
      }
    };
    
    // Calculate file type distribution
    files.forEach(file => {
      const type = file.type;
      stats.filesByType[type] = (stats.filesByType[type] || 0) + 1;
      
      // Calculate age distribution
      const daysDiff = Math.floor((now - file.created) / (1000 * 60 * 60 * 24));
      if (daysDiff === 0) {
        stats.filesByAge.today++;
      } else if (daysDiff <= 7) {
        stats.filesByAge.thisWeek++;
      } else if (daysDiff <= 30) {
        stats.filesByAge.thisMonth++;
      } else {
        stats.filesByAge.older++;
      }
    });
    
    return stats;
  }
  
  /**
   * Formats file size in human readable format
   * @param {number} bytes - Size in bytes
   * @returns {string} Formatted size string
   */
  static formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

module.exports = CleanupService;
